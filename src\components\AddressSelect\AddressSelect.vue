<script setup lang="ts">
import L from 'leaflet'

const { editable = true } = defineProps<{
  /**
   * 编辑模式还是详情模式
   */
  editable?: boolean
}>()

/**
 * 提交时发射事件
 */
const emit = defineEmits<{
  (e: 'change', data: { latitude: number, longitude: number, address: string }): void
}>()

const dialogVisible = defineModel<boolean>()

const DEFAULT_DATA = { latitude: 30.505399066242695, longitude: 103.62545013427736, address: '四川省成都市大邑县安仁古镇' }

const title = ref('地址选择')

const mapRef = useTemplateRef('mapRef')
let map: L.Map
const latlng = reactive({ lat: null, lng: null })
const marker = L.marker(latlng)
const onceInit = useOnceFn(() => {
  // 使用 id 为 map 的 div 容器初始化地图，同时指定地图的中心点和缩放级别
  map = L.map(MAP_ID, {
    center: [31.626748, 105.817774],
    zoom: 18,
    attributionControl: false,
  })
  // 加载高德地图
  L.tileLayer('//webrd02.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}', {
    tileSize: 256,
    maxZoom: 20,
    maxNativeZoom: 18,
    minZoom: 6,
    bounds: [
      [50.36729928578421, 68.55468750000001],
      [-3.6286541049128727, 141.32812500000003],
    ],
  }).addTo(map)
  marker.addTo(map)
  map.on('click', onClick)
})
</script>

<template>
  <el-dialog v-model="dialogVisible" destroy-on-close width="55.6vw" :title="title" append-to-body :close-on-click-modal="false">
    <div ref="mapRef" class="w-full h-600 bg-red" />
    <template #footer>
      <div>
        <el-button>取消</el-button>
        <el-button type="primary">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
