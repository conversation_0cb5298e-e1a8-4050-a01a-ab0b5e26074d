import type { MaybeRef } from 'vue'

export type UnrefFn<T> = T extends (...args: infer A) => infer R ? (...args: { [K in keyof A]: MaybeRef<A[K]> }) => R : never
/**
 * 返回一个只会调用原始函数一次的函数
 * @param {T} fn - 要调用一次的函数
 * @returns {void} 一个只会被调用一次的函数
 */
export function useOnceFn<T extends () => void>(fn: T) {
  let flag = true
  const onceFn = (...args) => {
    if (flag) {
      flag = false
      fn(...args)
    }
  }

  return onceFn as UnrefFn<T>
}
