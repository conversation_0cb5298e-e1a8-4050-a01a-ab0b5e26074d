<script lang="ts" setup>
import type { UploadFile, UploadRequestOptions, UploadUserFile } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { FileAPI, getFileUrl } from '@/api/system/file.ts'

const {
  dirName = 'image',
  tip,
} = defineProps<{
  /** 上传文件目录名 */
  dirName?: string
  tip?: string
}>()

const emit = defineEmits<{
  (e: 'change', fileList: string): void
}>()

const rawUrl = defineModel<string>()

const fileObj = ref<UploadUserFile>()

function changeValue() {
  needRefresh = false
  rawUrl.value = fileObj.value.objectName
  emit('change', rawUrl.value)
}

const loading = ref(false)
function upload(options: UploadRequestOptions) {
  if (loading.value)
    return
  const file = options.file
  loading.value = true
  fileObj.value.status = 'uploading'
  return FileAPI.upload(file, dirName)
    .then((res) => {
      fileObj.value.status = 'success'
      fileObj.value.url = res.url
      fileObj.value.objectName = res.objectName
      changeValue()
    })
    .catch(() => {
      fileObj.value = null
    })
    .finally(() => {
      loading.value = false
    })
}

/**
 * 删除图片
 */
// function remove(removeFile: UploadFile) {
//   const fileItem = fileList.value.find(file => file.uid == removeFile.uid)
//   if (fileItem) {
//     FileAPI.remove(fileItem.objectName)
//       .then(() => {
//         changeValue()
//       })
//       .finally(() => {})
//   }
// }
function remove() {
  nextTick(() => {
    changeValue()
  })
}

/**
 * 预览
 */
const showViewer = ref(false)
const previewImgList = ref([])
function preview(file: UploadFile) {
  previewImgList.value = [file.url]
  showViewer.value = true
}

/**
 * 反显图片
 */
/** 内部更新rawFiles时这里无需执行 */
let needRefresh = true
watch(
  rawUrl,
  () => {
    if (!needRefresh) {
      needRefresh = true
      return
    }

    fileObj.value = null
    if (!rawUrl.value)
      return

    getFileUrl(rawUrl.value).then((url) => {
      fileObj.value = { objectName: url, url, status: 'success', name: '' }
    })
  },
  { immediate: true },
)

/**
 * 供外部调用，判断是否全部上传成功
 */
function uploaded() {
  return !loading.value
}

defineExpose({ uploaded })
</script>

<template>
  <el-upload
    :http-request="upload"
    :show-file-list="false"
    :on-preview="preview"
    accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.svg,.jfif,.ico"
    :on-remove="remove"
    class="w-full"
  >
    <div v-loading="loading">
      <el-icon v-if="!fileObj"><Plus /></el-icon>
      <el-image v-else class="image-card w-150 h-150 rounded-6" fit="cover" :src="fileObj.url" />
    </div>

    <template #tip>
      <div text="12 #8290AC">{{ tip }}</div>
    </template>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" :teleported="true" hide-on-click-modal :url-list="previewImgList" @close="showViewer = false" />
  </el-upload>
</template>

<style lang="scss" scoped></style>
