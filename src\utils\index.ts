import type { AddrSearchInfo, LocateInfo } from '#/map'

/**
 * 判单是否是图片
 */
export function isImg(url: string) {
  return /\.(jpg|jpeg|png|gif|bmp|webp|svg|jfif|ico)$/i.test(url)
}

/**
 * 下载文件，支持传入文件流或者url
 */
export async function download(content: string | BlobPart, fileName: string) {
  try {
    let blob: Blob
    if (typeof content === 'string') {
      // 获取文件
      const response = await fetch(content)
      blob = await response.blob()
    }
    else {
      blob = new Blob([content])
    }

    // 创建下载链接
    const downloadUrl = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.style.display = 'none'

    // 设置下载属性
    link.href = downloadUrl
    link.download = fileName

    // 触发下载
    document.body.append(link)
    link.click()

    // 清理
    link.remove()
    URL.revokeObjectURL(downloadUrl)
  }
  catch (error) {
    console.error('下载文件失败:', error)
  }
}

/**
 * 经纬度转地址
 */
const tianToken = 'df88e602c4cba7497f8eb525f458a98c'
export async function getReverseGeocodingData(latitude: number, longitude: number) {
  // const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}`
  const url = `https://api.tianditu.gov.cn/geocoder?postStr={'lon':${longitude},'lat':${latitude},'ver':1}&type=geocode&tk=${tianToken}`

  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const data: LocateInfo = await response.json()
    return data.result.formatted_address // 返回位置信息
  }
  catch (error) {
    console.error('Error:', error)
    return null
  }
}

/**
 * 地址 =》 经纬度
 */
const amapServiceKeyList = ['c8ee7a9e90f6c679f3348e284a86a5c8', '5755195ee8340f609b735cd11a59dfcb', '18299e5c1960a93f0d991686d6b53478']
const STORAGE_KEY = 'map-key-index'
let keyIndex = Number(localStorage.getItem(STORAGE_KEY)) || 0
// 记录尝试次数
let tryCount = 0
export async function searchAddress(address: string) {
  // https://restapi.amap.com/v3/geocode/geo
  const url = `https://restapi.amap.com/v5/place/text?key=${amapServiceKeyList[keyIndex]}&keywords=${address}`

  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error('Network response was not ok')
    }

    const data: AddrSearchInfo = await response.json()
    if (data.status === '1') {
      tryCount = 0
      return data.pois // 返回poi列表
    }
    else if (tryCount < amapServiceKeyList.length) {
      tryCount++
      // 失效后切换key
      keyIndex = (keyIndex + 1) % amapServiceKeyList.length
      localStorage.setItem(STORAGE_KEY, `${keyIndex}`)
      return searchAddress(address)
    }
    else {
      ElMessage.error('地理编码服务失效，请联系管理员')
      throw new Error('地理编码服务失效，请联系管理员')
    }
  }
  catch (error) {
    throw new Error(error)
  }
}
