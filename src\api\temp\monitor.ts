import request from '@/utils/request'

export const MonitorAPI = {
    page(params: PageQuery<Monitor>) {
        return request<any, Records<Monitor>>({
            url: '/api/v1/manage/web/device/camera/page',
            method: 'get',
            params,
        })
    },

}


/**
 * CameraVO
 */
export interface Monitor {
    /**
     * 区域名称
     */
    areaName?: string;
    /**
     * 批次号
     */
    batchNum?: string;
    /**
     * 通道号
     */
    channelId?: string;
    /**
     * 通道名称
     */
    channelName?: string;
    /**
     * 客户id
     */
    customerId?: number;
    /**
     * 客户名称
     */
    customerName?: string;
    /**
     * 设备号
     */
    deviceId?: string;
    /**
     * 对外型号名称
     */
    externalModel?: string;
    id?: number;
    /**
     * 设备型号id
     */
    innerModel?: number;
    /**
     * 设备型号名称
     */
    innerModelName?: string;
    /**
     * 修改时间
     */
    modifyTime?: string;
    /**
     * 是否在线
     */
    online?: boolean;
    /**
     * 设备编码(系统内部编号)
     */
    serialNum?: string;
    /**
     * 快照URL
     */
    snapUrl?: string;
    /**
     * 设备类型
     * 1 杀虫灯
     * 2 墒情
     * 3 虫情分析
     * 4 监控
     */
    type?: number;
    /**
     * 设备类型名称
     * 映射json value
     */
    typeName?: string;
}
