<script setup lang="ts">
import type { RouteRecordRawExt } from '@/router'

const { routeData, rootPath, level } = defineProps<{
  routeData: RouteRecordRawExt
  rootPath: string
  level: number
}>()

const route = useRoute()

const parentRoute = computed(() => route.matched[level])
const router = useRouter()
function handleMenuClick(path: string) {
  router.push(path)
}

function handledRootPath() {
  // 如果有子路由但只有1个子路由，且meta.singleChild为true，则渲染为单一菜单项
  if (routeData.meta.singleChild) {
    return `${rootPath}/${routeData.children?.[0].path}`
  }
  // 否则渲染为当前路由路径
  else {
    return rootPath
  }
}
</script>

<template>
  <!-- 如果有子路由且子路由大于1个，则渲染为子菜单 -->
  <el-sub-menu v-if="routeData.children && !routeData.meta.singleChild" :index="rootPath">
    <template #title>
      <i
        :class="[`i-sidebar-${routeData.meta.icon}`, { '!text-primary': parentRoute?.path === rootPath }]"
        class="mr-8 text-18"
      />
      <span :class="{ 'text-primary': parentRoute?.path === rootPath }" class="text-14">{{ routeData.meta.title }}</span>
    </template>
    <MenuItem v-for="item in routeData.children" :key="`${rootPath}/${item.path}`" :level="level + 1" :routeData="item" :rootPath="`${rootPath}/${item.path}`" />
  </el-sub-menu>

  <!-- 其余渲染为普通菜单项 -->
  <el-menu-item
    v-else
    :index="handledRootPath()"
    @click="handleMenuClick(handledRootPath())"
  >
    <i
      :class="[`i-sidebar-${routeData.meta.icon}`]"
      class="mr-8 text-18"
    />
    <span class="text-14"> {{ routeData.meta.title }} </span>
  </el-menu-item>
</template>

<style scoped lang="scss"></style>
