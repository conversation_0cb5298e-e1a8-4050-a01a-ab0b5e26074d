import { dayjs } from 'element-plus'
import { createApp } from 'vue'
import router from '@/router'
import { setupStore } from '@/store'
import App from './App.vue'
import '@/style/index.scss'
import 'normalize.css'
// 原子化css
import 'uno.css'
import './permission'
// svg-icon
// import 'virtual:svg-icons-register'
import 'animate.css'
import 'leaflet/dist/leaflet.css'
// 自动为某些默认事件（如 touchstart、wheel 等）添加 { passive: true },提升滚动性能并消除控制台的非被动事件监听警告
import 'default-passive-events'

const app = createApp(App)
setupStore(app)
dayjs.en.weekStart = 1

app.use(router)
app.mount('#app')
