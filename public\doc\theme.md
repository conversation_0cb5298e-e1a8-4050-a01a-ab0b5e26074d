# 主题适配文档

### 项目结构

```
src/style/
├── index.scss           # 样式入口文件
├── theme/
│   ├── index.scss      # 主题配置入口
│   └── dark.scss       # 暗黑主题变量
├── variable.scss       # 项目变量
└── element.scss        # Element Plus组件样式覆盖
```

### 实现方式

**1. 核心原理**

本质上就是改变css变量

**2. 实现方式**

**1. SCSS变量覆盖**

- 使用 `@forward` 语法覆盖Element Plus的SCSS变量
- 分别为浅色和深色主题定义颜色配置

```scss
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #135afa,
    ),
    'success': (
      'base': #21ba45,
    ),
    'warning': (
      'base': #f2711c,
    ),
    'danger': (
      'base': #db2828,
    ),
    'error': (
      'base': #db2828,
    ),
    'info': (
      'base': #42b8dd,
    ),
  )
);
```

**2. 主题切换逻辑**

- 通过DOM类名（`.light`、`.dark`）控制主题
- 使用 `document.startViewTransition` API实现平滑过渡动画

## 主题配置指南

### 1. 创建主题配置文件

**src/style/theme/index.scss**

```scss
// 浅色主题配置
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #135afa,
    ),
    'success': (
      'base': #21ba45,
    ),
    'warning': (
      'base': #f2711c,
    ),
    'danger': (
      'base': #db2828,
    ),
    'error': (
      'base': #db2828,
    ),
    'info': (
      'base': #42b8dd,
    ),
  )
);

// 引入暗黑主题
@use 'dark';
```

### 2. 配置暗黑主题

**src/style/theme/dark.scss**

```scss
// 暗黑主题配置
@forward 'element-plus/theme-chalk/src/dark/var.scss' with (
  $colors: (
    'primary': (
      'base': #2153c3,
    ),
  ),
  $bg-color: (
    'page': #0a0a0a,
    '': #626aef,
    'overlay': #1d1e1f,
  )
);
```

### 3. 响应式主题切换

```typescript
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
function updateTheme() {
  if (isDark.value !== mediaQuery.matches) {
    document.documentElement.classList.toggle('dark', !isDark.value)
    isDark.value = !isDark.value
  }
}

mediaQuery.addEventListener('change', () => {
  updateTheme()
})
```

## 参考资源

- [Element Plus 暗黑模式文档](https://element-plus.org/zh-CN/guide/dark-mode.html)
- [Element Plus 自定义主题文档](https://element-plus.org/zh-CN/guide/theming.html)
- [View Transitions API](https://developer.mozilla.org/en-US/docs/Web/API/View_Transitions_API)
- [ElementPlus主题切换-掘金](https://juejin.cn/post/7444109900878151731?searchId=20250722103341ED8796E1D95A8958F9A7)
- [VueUse、View Transitions Api主题切换-掘金](https://juejin.cn/post/7326707110212485130)
