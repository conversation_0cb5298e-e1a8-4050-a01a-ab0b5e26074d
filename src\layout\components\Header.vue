<script setup lang="ts">
import type { RouteLocationMatched } from 'vue-router'
import { Expand, Fold } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useAppStore } from '@/store/app'
import { useUserStore } from '@/store/user.ts'

const route = useRoute()
const matchedRoutes = computed(() => {
  if (route.matched[0].meta.singleChild) {
    return [route.matched[0]]
  }
  else {
    return route.matched
  }
})

const appStore = useAppStore()
const { isCollapse } = storeToRefs(appStore)

function toggleSidebar() {
  isCollapse.value = !isCollapse.value
}

const router = useRouter()
function to(info: RouteLocationMatched) {
  // 跳转到父路由的第一个子路由
  const fullPath = `${info.path}/${info.children.at(0).path}`
  router.push(fullPath)
}

const userStore = useUserStore()
function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    lockScroll: false,
  }).then(() => {
    userStore.logout()
  })
}
</script>

<template>
  <header class="app-header flex items-center px-16 bg-$el-bg-color" b="b-1 solid $el-border-color-light">
    <div class="flex items-center">
      <el-icon class="cursor-pointer" text="16 text-secondary" @click="toggleSidebar">
        <Expand v-if="isCollapse" />
        <Fold v-else />
      </el-icon>
      <div class="ml-16 leading-20 flex items-center" text="14 text-secondary">
        <transition-group name="breadcrumb">
          <span v-for="(item, index) in matchedRoutes" :key="item.path" class="flex items-center">
            <span v-if="index !== 0" class="mx-4">/</span>
            <!--          最后一级 -->
            <span v-if="index === matchedRoutes.length - 1">{{ item.meta.title }}</span>
            <!--          非最后一级 -->
            <el-button v-else link hover="!text-primary" class="!text-textPrimary" @click="to(item)">{{ item.meta.title }}</el-button>
          </span>
        </transition-group>
      </div>
    </div>
    <aside class="ml-auto flex items-center gap-24 mx-12">
      <Theme />

      <MenuSearch />

      <el-dropdown trigger="click">
        <div class="flex items-center gap-8 cursor-pointer">
          <img src="@/assets/layout/avatar.webp" class="size-32" />
          <span text="textPrimary">Axlesd</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </aside>
  </header>
</template>

<style scoped lang="scss">
.app-header {
  height: var(--app-header-h);
}
</style>
